import React from 'react';
import DeviceDropdown from './DeviceDropdown';
import TestButton from './TestButton';
import MicLevelIndicator from './MicLevelIndicator';
import VolumeControl from './VolumeControl';

/**
 * SpeakerSettings Component
 * Complete speaker configuration section
 * @param {Array} speakerDevices - Available audio output devices
 * @param {string} speakerDeviceId - Selected speaker device ID
 * @param {function} onSpeakerDeviceChange - Speaker device change handler
 * @param {boolean} permissions - Microphone permission status (needed for speaker enumeration)
 * @param {function} onTestSpeaker - Speaker test handler
 * @param {boolean} isSpeakerTesting - Whether speaker is being tested
 * @param {number} speakerAudioLevel - Current speaker audio level (0-100)
 * @param {number} outputVolume - Current output volume (0-100)
 * @param {function} onOutputVolumeChange - Output volume change handler
 */
function SpeakerSettings({
  speakerDevices = [],
  speakerDeviceId,
  onSpeakerDeviceChange,
  permissions = { microphone: false },
  onTestSpeaker,
  isSpeakerTesting = false,
  speakerAudioLevel = 0,
  outputVolume = 100,
  onOutputVolumeChange
}) {
  // Console log the list of speakers
  console.log('List of speakers:', speakerDevices);

  const getSpeakerTestButtonText = () => {
    if (isSpeakerTesting) {
      return 'Stop Test';
    }
    return 'Test Speaker';
  };

  const handleSetAsSystemDefault = () => {
    const selectedDevice = speakerDevices.find(device => device.deviceId === speakerDeviceId);
    const deviceName = selectedDevice?.label || 'selected device';

    // Show instructions for macOS
    const instructions = `To set "${deviceName}" as your system default:

1. Open System Preferences/Settings
2. Go to Sound → Output
3. Select "${deviceName}"
4. Close System Preferences

This will ensure all meeting audio uses your selected speakers.`;

    alert(instructions);
  };

  const isDisabled = !permissions.microphone || speakerDevices.length === 0;
  const hasSelectedDevice = speakerDeviceId && speakerDevices.length > 0;

  // Check if selected device is likely the system default
  const selectedDevice = speakerDevices.find(device => device.deviceId === speakerDeviceId);
  const isLikelySystemDefault = selectedDevice?.deviceId === 'default' ||
                                selectedDevice?.label?.toLowerCase().includes('default');

  // Show warning if not using system default
  const showSystemDefaultWarning = hasSelectedDevice && !isLikelySystemDefault;

  return (
    <div className="settings-section speaker-settings-section">
      <div className="grid-container speaker-grid">
        <div className="grid-cell left">
          <span className="setting-label">Speaker</span>
        </div>
        <div className="grid-cell left">
          <span className="setting-sublabel">Choose Speaker</span>
        </div>
        <div className="grid-cell center">
          <DeviceDropdown
            devices={speakerDevices}
            selectedDeviceId={speakerDeviceId}
            onDeviceChange={onSpeakerDeviceChange}
            hasPermission={permissions.microphone}
            permissionMessage="Grant microphone permission to see devices"
            noDevicesMessage="No speaker devices found"
            defaultDeviceName="Default Speaker"
            selectPlaceholder="Select speaker"
          />
        </div>

        <div className="grid-cell center">
          {/* Empty cell */}
        </div>
        <div className="grid-cell left">
          <span className="setting-sublabel">Test Speaker</span>
        </div>
        <div className="grid-cell left">
          <TestButton
            onClick={onTestSpeaker}
            isActive={isSpeakerTesting}
            activeText="Stop Test"
            inactiveText={getSpeakerTestButtonText()}
            disabled={isDisabled}
          />
        </div>

        <div className="grid-cell center">
          {/* Empty cell */}
        </div>
        <div className="grid-cell left">
          <span className="setting-sublabel">Output Level:</span>
        </div>
        <div className="grid-cell center">
          <MicLevelIndicator
            level={speakerAudioLevel}
            isActive={isSpeakerTesting || speakerAudioLevel > 0}
          />
        </div>

        <div className="grid-cell center">
          {/* Empty cell */}
        </div>
        <div className="grid-cell left">
          <span className="setting-sublabel">Output Volume:</span>
        </div>
        <div className="grid-cell center">
          <VolumeControl
            value={outputVolume}
            onChange={onOutputVolumeChange}
            min={0}
            max={100}
          />
        </div>

        {/* System Default Helper */}
        <div className="grid-cell center">
          {/* Empty cell */}
        </div>
        <div className="grid-cell left">
          <span className="setting-sublabel">System Default:</span>
        </div>
        <div className="grid-cell center">
          <TestButton
            onClick={handleSetAsSystemDefault}
            isActive={false}
            activeText=""
            inactiveText="Set as Default"
            disabled={!hasSelectedDevice}
            title="Get instructions to set this device as your system default"
          />
        </div>

        {/* System Default Warning */}
        {showSystemDefaultWarning && (
          <>
            <div className="grid-cell center">
              {/* Empty cell */}
            </div>
            <div className="grid-cell left" style={{ gridColumn: '1 / -1' }}>
              <div style={{
                padding: '8px 12px',
                backgroundColor: '#fff3cd',
                border: '1px solid #ffeaa7',
                borderRadius: '4px',
                fontSize: '12px',
                color: '#856404'
              }}>
                ⚠️ For best results, set "{selectedDevice?.label}" as your system default in macOS Sound Settings
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

export default SpeakerSettings;

import React, { useEffect, useRef } from 'react';
import { AudioTrack } from '@livekit/components-react';

/**
 * CustomAudioTrack - Enhanced AudioTrack that supports speaker device switching
 * This component wraps the LiveKit AudioTrack and applies setSinkId() to route audio
 * to the selected speaker device, just like Google Meet.
 * 
 * @param {Object} props - Component props
 * @param {string} speakerDeviceId - Selected speaker device ID
 * @param {Object} trackRef - LiveKit track reference
 * @param {Function} onSubscriptionStatusChanged - Subscription status callback
 */
export function CustomAudioTrack({ 
  speakerDeviceId, 
  trackRef, 
  onSubscriptionStatusChanged,
  ...props 
}) {
  const audioElementRef = useRef(null);
  const currentDeviceIdRef = useRef(null);

  // Apply speaker device when it changes
  useEffect(() => {
    const applyAudioOutputDevice = async () => {
      // Skip if no device selected or same device
      if (!speakerDeviceId || speakerDeviceId === currentDeviceIdRef.current) {
        return;
      }

      // Find the audio element created by LiveKit's AudioTrack
      const findAudioElement = () => {
        if (audioElementRef.current) {
          // Look for audio element within our component
          const audioElement = audioElementRef.current.querySelector('audio');
          if (audioElement) {
            return audioElement;
          }
        }
        return null;
      };

      // Try to find audio element with retries
      let attempts = 0;
      const maxAttempts = 10;
      
      const tryApplyDevice = async () => {
        const audioElement = findAudioElement();
        
        if (audioElement && audioElement.setSinkId) {
          try {
            await audioElement.setSinkId(speakerDeviceId);
            currentDeviceIdRef.current = speakerDeviceId;
            console.log(`Audio output switched to device: ${speakerDeviceId}`);
            return true;
          } catch (error) {
            console.warn('Failed to set audio output device:', error);
            return false;
          }
        }
        
        // Retry if audio element not found yet
        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(tryApplyDevice, 100);
        }
        return false;
      };

      tryApplyDevice();
    };

    // Small delay to ensure AudioTrack has rendered
    setTimeout(applyAudioOutputDevice, 50);
  }, [speakerDeviceId]);

  // Monitor for new audio elements when track changes
  useEffect(() => {
    if (!trackRef || !speakerDeviceId) return;

    const observer = new MutationObserver(() => {
      const audioElement = audioElementRef.current?.querySelector('audio');
      if (audioElement && audioElement.setSinkId && speakerDeviceId !== currentDeviceIdRef.current) {
        audioElement.setSinkId(speakerDeviceId)
          .then(() => {
            currentDeviceIdRef.current = speakerDeviceId;
            console.log(`Audio output applied to new element: ${speakerDeviceId}`);
          })
          .catch(error => {
            console.warn('Failed to set audio output device on new element:', error);
          });
      }
    });

    if (audioElementRef.current) {
      observer.observe(audioElementRef.current, {
        childList: true,
        subtree: true
      });
    }

    return () => observer.disconnect();
  }, [trackRef, speakerDeviceId]);

  return (
    <div ref={audioElementRef} style={{ display: 'contents' }}>
      <AudioTrack
        trackRef={trackRef}
        onSubscriptionStatusChanged={onSubscriptionStatusChanged}
        {...props}
      />
    </div>
  );
}

export default CustomAudioTrack;
